"use client";

import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { getTradingPosts, createTradingPost, togglePostLike, getPostComments, createComment, updateTradingPost, deleteTradingPost } from '../lib/database';
import { useAuth } from '../hooks/useAuth';
import type { TradingPost } from '../lib/supabase';

interface TradingPostsContextType {
  posts: TradingPost[];
  loading: boolean;
  createPost: (postData: any) => Promise<boolean>;
  toggleLike: (postId: string) => Promise<void>;
  refreshPosts: () => Promise<void>;
  refreshPostsAfterProfileUpdate: () => Promise<void>;
  filterPosts: (filters: {
    category?: string;
    search?: string;
    sortBy?: 'recent' | 'likes' | 'comments';
  }) => void;
  getComments: (postId: string) => Promise<any[]>;
  addComment: (postId: string, content: string) => Promise<boolean>;
  updatePost: (postId: string, updates: any) => Promise<boolean>;
  deletePost: (postId: string) => Promise<boolean>;
}

const TradingPostsContext = createContext<TradingPostsContextType | undefined>(undefined);

export function TradingPostsProvider({ children }: { children: ReactNode }) {
  const [posts, setPosts] = useState<TradingPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<{
    category: string;
    search: string;
    sortBy: 'recent' | 'likes' | 'comments';
  }>({
    category: 'all',
    search: '',
    sortBy: 'recent'
  });
  const { user } = useAuth();

  const loadPosts = async () => {
    setLoading(true);
    try {
      const fetchedPosts = await getTradingPosts({
        category: filters.category !== 'all' ? filters.category : undefined,
        search: filters.search || undefined,
        sortBy: filters.sortBy,
        limit: 50
      });
      setPosts(fetchedPosts);
    } catch (error) {
      console.error('Error loading posts:', error);
      setPosts([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPosts();
  }, [filters.category, filters.search, filters.sortBy]);

  const createPost = async (postData: {
    symbol: string;
    type: 'Long' | 'Short';
    entry: string;
    target: string;
    stopLoss: string;
    timeframe: string;
    description: string;
    tags: string;
    status: string;
    image: File | null;
    category: string;
  }): Promise<boolean> => {
    console.log('CreatePost called with:', postData);
    console.log('User:', user);

    if (!user) {
      console.error('No user found');
      return false;
    }

    try {
      // Handle image upload if present
      let imageUrl = null;
      if (postData.image) {
        // Convert the uploaded image to a data URL to match the preview
        imageUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.readAsDataURL(postData.image!);
        });
      } else {
        // Use a placeholder if no image is uploaded
        imageUrl = 'https://picsum.photos/400/300?random=' + Math.floor(Math.random() * 1000);
      }

      // Calculate risk/reward ratio
      const entry = parseFloat(postData.entry.replace(/[^0-9.-]+/g, ''));
      const target = parseFloat(postData.target.replace(/[^0-9.-]+/g, ''));
      const stopLoss = parseFloat(postData.stopLoss.replace(/[^0-9.-]+/g, ''));

      let riskRewardRatio = '1:1';
      if (entry && target && stopLoss) {
        const risk = Math.abs(entry - stopLoss);
        const reward = Math.abs(target - entry);
        if (risk > 0) {
          const ratio = (reward / risk).toFixed(1);
          riskRewardRatio = `1:${ratio}`;
        }
      }

      const postToCreate = {
        user_id: user.id,
        symbol: postData.symbol,
        position_type: postData.type,
        entry_price: postData.entry,
        target_price: postData.target,
        stop_loss: postData.stopLoss,
        risk_reward_ratio: riskRewardRatio,
        timeframe: postData.timeframe,
        description: postData.description,
        image_url: imageUrl,
        status: postData.status as any,
        tags: postData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        category: postData.category as any,
        hidden: false
      };

      console.log('About to create post:', postToCreate);

      const newPost = await createTradingPost(postToCreate);

      if (newPost) {
        console.log('Post created successfully:', newPost);
        // Refresh posts to show the new one
        await loadPosts();
        return true;
      } else {
        console.error('Failed to create post - no data returned');
      }
    } catch (error) {
      console.error('Error creating post:', error);
    }

    return false;
  };

  const toggleLike = async (postId: string) => {
    if (!user) return;

    try {
      await togglePostLike(postId, user.id);
      // Refresh posts to update like counts
      await loadPosts();
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  const filterPosts = useCallback((newFilters: {
    category?: string;
    search?: string;
    sortBy?: 'recent' | 'likes' | 'comments';
  }) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const refreshPosts = async () => {
    await loadPosts();
  };

  const refreshPostsAfterProfileUpdate = async () => {
    // Refresh posts to show updated profile info
    await loadPosts();
  };

  const getComments = async (postId: string) => {
    try {
      const comments = await getPostComments(postId);
      return comments;
    } catch (error) {
      console.error('Error fetching comments:', error);
      return [];
    }
  };

  const addComment = async (postId: string, content: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const comment = await createComment({
        post_id: postId,
        user_id: user.id,
        content: content
      });

      if (comment) {
        // Refresh posts to update comment count
        await loadPosts();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error creating comment:', error);
      return false;
    }
  };

  const updatePost = async (postId: string, updates: any): Promise<boolean> => {
    if (!user) return false;

    try {
      const updatedPost = await updateTradingPost(postId, updates, user.id);
      if (updatedPost) {
        await loadPosts();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating post:', error);
      return false;
    }
  };

  const deletePost = async (postId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const success = await deleteTradingPost(postId, user.id);
      if (success) {
        await loadPosts();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting post:', error);
      return false;
    }
  };

  return (
    <TradingPostsContext.Provider value={{
      posts,
      loading,
      createPost,
      toggleLike,
      refreshPosts,
      refreshPostsAfterProfileUpdate,
      filterPosts,
      getComments,
      addComment,
      updatePost,
      deletePost
    }}>
      {children}
    </TradingPostsContext.Provider>
  );
}

export function useTradingPosts() {
  const context = useContext(TradingPostsContext);
  if (context === undefined) {
    throw new Error('useTradingPosts must be used within a TradingPostsProvider');
  }
  return context;
}
