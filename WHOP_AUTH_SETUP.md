# Whop Authentication Integration

This document explains how to set up and use Whop authentication with your trading hub application.

## Overview

The application now integrates with Who<PERSON> to:
- Authenticate users using their Whop accounts
- Sync user profile data from Whop to Supabase
- Associate all posts with the correct authenticated user
- Support both token-based and user ID-based authentication

## Setup Instructions

### 1. Database Migration

First, update your Supabase database to include Whop user fields:

1. Go to your Supabase dashboard → **SQL Editor**
2. Copy and paste the contents of `supabase/migrations/add_whop_fields.sql`
3. Click **Run** to execute the migration

This adds the following fields to the `profiles` table:
- `whop_user_id` - Unique Whop user identifier
- `whop_profile_url` - URL to user's Whop profile
- `whop_discord_id` - Associated Discord ID

### 2. Environment Variables

Ensure these environment variables are set in your `.env` file:

```env
# Whop Configuration
WHOP_API_KEY=your_whop_api_key
NEXT_PUBLIC_WHOP_APP_ID=your_app_id
NEXT_PUBLIC_WHOP_AGENT_USER_ID=your_agent_user_id
NEXT_PUBLIC_WHOP_COMPANY_ID=your_company_id

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Test the Integration

Run the test script to verify everything is working:

```bash
node scripts/test-whop-auth.js
```

## How It Works

### Authentication Flow

1. **URL Parameters**: The app checks for Whop user data in URL parameters:
   - `?whop_user_id=USER_ID` - Direct user ID
   - `?token=TOKEN` - Authentication token
   - `?user_id=USER_ID` - Alternative user ID parameter

2. **User Data Retrieval**: 
   - If a token is provided, it's used to authenticate and fetch user data
   - If only a user ID is provided, the server-side API fetches user data
   - User data includes: ID, username, email, profile picture, Discord ID

3. **Supabase Sync**:
   - User data is synced to the Supabase `profiles` table
   - If a profile exists, it's updated with latest Whop data
   - If no profile exists, a new one is created

4. **Session Management**:
   - A compatible user session is created for the application
   - All subsequent actions (posts, likes, comments) use the authenticated user

### Fallback Behavior

- If no Whop authentication is detected, the app checks for existing Supabase auth sessions
- If no authentication is found, a mock user is created for development purposes
- This ensures the app works in all environments

## API Endpoints

### GET /api/auth/whop?token=TOKEN

Authenticates a user using a Whop token and returns user data.

**Response:**
```json
{
  "id": "user_123",
  "email": "<EMAIL>",
  "username": "username",
  "profilePictureUrl": "https://...",
  "discordId": "discord_123",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

### POST /api/auth/whop

Retrieves user data using a Whop user ID.

**Request:**
```json
{
  "userId": "user_123"
}
```

**Response:** Same as GET endpoint

## Usage in Whop

When your app is embedded in Whop, users will be automatically authenticated. The Whop platform typically provides user context through:

1. **URL Parameters**: User ID and/or authentication tokens
2. **Iframe Context**: User data passed through iframe communication
3. **API Access**: Server-side API calls using your Whop API key

## Development Testing

For local development, you can test authentication by:

1. **Mock User**: The app creates a mock user when no Whop auth is detected
2. **URL Parameters**: Add `?whop_user_id=USER_ID` to your local URL
3. **Test Script**: Run `node scripts/test-whop-auth.js` to verify setup

## Database Schema

The `profiles` table now includes:

```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  verified BOOLEAN DEFAULT FALSE,
  followers_count INTEGER DEFAULT 0,
  bio TEXT,
  whop_user_id TEXT UNIQUE,           -- New: Whop user ID
  whop_profile_url TEXT,              -- New: Whop profile URL
  whop_discord_id TEXT,               -- New: Discord ID from Whop
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Troubleshooting

### Common Issues

1. **"No user found" errors**: Check that WHOP_API_KEY is correct and user ID exists
2. **Database errors**: Ensure the migration has been run in Supabase
3. **Authentication not working**: Verify environment variables are set correctly

### Debug Mode

Enable debug logging by checking the browser console for:
- User authentication attempts
- Whop API responses
- Profile sync operations
- Post creation with user associations

### Support

If you encounter issues:
1. Run the test script to verify setup
2. Check browser console for error messages
3. Verify Whop API key permissions
4. Ensure Supabase database migration was successful
