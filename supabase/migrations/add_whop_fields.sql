-- Migration to add Whop user fields to profiles table
-- Run this in your Supabase SQL Editor to update existing database

-- Add new columns for Whop user data
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS whop_user_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS whop_profile_url TEXT,
ADD COLUMN IF NOT EXISTS whop_discord_id TEXT;

-- Create index on whop_user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_whop_user_id ON profiles(whop_user_id);

-- Add comment to document the new fields
COMMENT ON COLUMN profiles.whop_user_id IS 'Unique identifier from Whop platform';
COMMENT ON COLUMN profiles.whop_profile_url IS 'URL to user profile on Whop platform';
COMMENT ON COLUMN profiles.whop_discord_id IS 'Discord ID associated with Whop account';
