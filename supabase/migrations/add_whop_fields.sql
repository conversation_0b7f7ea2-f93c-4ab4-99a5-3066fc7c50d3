-- Migration to add Whop user fields to profiles table
-- Run this in your Supabase SQL Editor to update existing database

-- Add new columns for Whop user data
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS whop_user_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS whop_profile_url TEXT,
ADD COLUMN IF NOT EXISTS whop_username TEXT,
ADD COLUMN IF NOT EXISTS whop_city TEXT,
ADD COLUMN IF NOT EXISTS whop_country TEXT,
ADD COLUMN IF NOT EXISTS whop_phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS whop_banner_url TEXT,
ADD COLUMN IF NOT EXISTS whop_created_at TIMESTAMP WITH TIME ZONE;

-- Create index on whop_user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_whop_user_id ON profiles(whop_user_id);

-- Add comments to document the new fields
COMMENT ON COLUMN profiles.whop_user_id IS 'Unique identifier from Whop platform';
COMMENT ON COLUMN profiles.whop_profile_url IS 'URL to user profile on Whop platform';
COMMENT ON COLUMN profiles.whop_username IS 'Username from Whop platform';
COMMENT ON COLUMN profiles.whop_city IS 'City from Whop user profile';
COMMENT ON COLUMN profiles.whop_country IS 'Country from Whop user profile';
COMMENT ON COLUMN profiles.whop_phone_verified IS 'Phone verification status from Whop';
COMMENT ON COLUMN profiles.whop_banner_url IS 'Banner image URL from Whop profile';
COMMENT ON COLUMN profiles.whop_created_at IS 'Account creation date from Whop';
