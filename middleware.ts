import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Allow these specific routes to pass through
  const allowedRoutes = [
    '/',
    '/settings',
    '/community',
    '/_next',
    '/api',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml'
  ];
  
  // Check if the path starts with any allowed route
  const isAllowedRoute = allowedRoutes.some(route => {
    if (route === '/') return pathname === '/';
    return pathname.startsWith(route);
  });
  
  // Allow community dynamic routes like /community/[id]
  const isCommunityRoute = /^\/community\/[^\/]+$/.test(pathname);
  
  // If it's not an allowed route and not a community route, redirect to home
  if (!isAllowedRoute && !isCommunityRoute) {
    const url = request.nextUrl.clone();
    url.pathname = '/';
    return NextResponse.redirect(url);
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
