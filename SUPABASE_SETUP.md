# Supabase Setup Guide

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `trading-hub` (or your preferred name)
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

## Step 2: Get Your Project Keys

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon key** (public key, starts with `eyJ...`)
   - **Service role key** (secret key, starts with `eyJ...`)

## Step 3: Update Environment Variables

Add these to your `.env` file:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Step 4: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the entire contents of `supabase/schema.sql`
3. Paste it into the SQL Editor
4. Click "Run" to execute the schema

This will create:
- All necessary tables (profiles, trading_posts, communities, etc.)
- Row Level Security policies
- Database functions for counting
- Proper indexes for performance

## Step 5: Set Up Storage (for images)

1. Go to **Storage** in your Supabase dashboard
2. Click "Create a new bucket"
3. Name it `trading-images`
4. Make it **Public** (so images can be viewed)
5. Click "Create bucket"

## Step 6: Configure Authentication

1. Go to **Authentication** → **Settings**
2. Under **Site URL**, add your domain:
   - For development: `http://localhost:3000`
   - For production: `https://yourdomain.com`
3. Under **Redirect URLs**, add:
   - `http://localhost:3000/auth/callback` (development)
   - `https://yourdomain.com/auth/callback` (production)

### Optional: Enable Social Login

If you want Google/GitHub/Discord login:

1. Go to **Authentication** → **Providers**
2. Enable the providers you want
3. Add the required OAuth credentials from each provider

## Step 7: Test the Setup

1. Start your development server: `npm run dev`
2. The app should now connect to Supabase
3. Try creating a trading post to test the database connection

## Database Tables Created

### `profiles`
- User profiles with username, avatar, verification status
- Linked to Supabase Auth users

### `trading_posts`
- Trading setups with all the details (symbol, entry, target, etc.)
- Includes likes/comments counts and tags

### `post_likes` & `post_comments`
- Social features for posts

### `communities` (for future use)
- Community data structure ready for when you enable it

### `community_members` & `community_reviews`
- Community membership and review system

## Security Features

- **Row Level Security (RLS)** enabled on all tables
- Users can only modify their own data
- Public read access for posts and profiles
- Secure policies for likes and comments

## Next Steps

After setup, you can:

1. **Replace mock data** with real Supabase calls
2. **Add authentication UI** for login/signup
3. **Implement image uploads** for trading charts
4. **Add real-time features** using Supabase subscriptions
5. **Enable communities** when ready

## Troubleshooting

### Common Issues:

1. **"Invalid API key"**: Check your environment variables
2. **"Table doesn't exist"**: Make sure you ran the schema.sql
3. **"Permission denied"**: Check RLS policies are set up correctly
4. **Images not uploading**: Verify storage bucket is created and public

### Getting Help:

- Check Supabase docs: [supabase.com/docs](https://supabase.com/docs)
- Join Supabase Discord for community support
- Check the browser console for detailed error messages
