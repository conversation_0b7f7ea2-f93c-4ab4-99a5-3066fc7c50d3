# Trading Discovery Hub

A clean, white-themed discovery page for trading servers on Discord to migrate to Whop. This platform allows traders to share their setups and users to discover trading opportunities in an Instagram FYP-style interface.

## Features

### 🎯 Clean White Theme Design
- Minimal design with thin borders and subtle shadows
- Inner shadows on buttons and cards for depth
- Neutral color palette with strategic use of green/blue/red for actions
- Instagram FYP-inspired layout

### 📊 Trading Post Cards
- **Trader Profile**: Avatar, name, verification status, follower count
- **Setup Details**: Symbol, position type (Long/Short), entry/target/stop loss prices
- **Risk Management**: Automatic risk/reward ratio calculation
- **Visual Elements**: Chart images, position type badges, timeframe indicators
- **Social Features**: Like, comment, and share functionality
- **Tags**: Categorization with hashtags

### 🔍 Discovery Features
- **Search**: Find setups by symbol, description, tags, or trader name
- **Filtering**: Filter by asset type (Crypto, Forex, Stocks, Options)
- **Sorting**: Sort by recent, most liked, or most discussed
- **Results Summary**: Shows filtered results count

### ✨ Post Creation
- **Comprehensive Form**: All trading setup fields with validation
- **Image Upload**: Chart image upload with preview
- **Real-time Calculations**: Automatic risk/reward ratio calculation
- **Tag System**: Easy tagging for categorization
- **Preview**: See how the post will look before publishing

## Tech Stack

- **Framework**: Next.js 14 with TypeScript
- **UI Components**: Whop's Frosted UI design system
- **Styling**: Tailwind CSS with custom shadow utilities
- **Icons**: Custom SVG icon components
- **State Management**: React hooks (useState, useMemo)

## Design Philosophy

### Instagram FYP Meets Trading
- **Card-based Layout**: Each trading setup is presented as a clean card
- **Visual Hierarchy**: Clear separation between trader info, chart, and setup details
- **Engagement Focused**: Like, comment, and share buttons prominently displayed
- **Mobile-first**: Responsive grid layout that works on all devices

### Clean White Theme
- **Minimal Shadows**: Subtle box-shadows for depth without overwhelming
- **Inner Shadows**: Buttons and input fields have inner shadows for tactile feel
- **Neutral Colors**: Primarily white and gray with strategic color usage:
  - **Green**: Long positions, targets, positive actions
  - **Red**: Short positions, stop losses, negative actions  
  - **Blue**: Primary actions, links, verification badges
  - **Gray**: Secondary text, borders, backgrounds

### Trading-Specific Features
- **Position Type Indicators**: Clear Long/Short badges with trending icons
- **Price Levels**: Organized display of entry, target, and stop loss
- **Risk Management**: Prominent risk/reward ratio display
- **Timeframe Context**: Clear indication of trading timeframe
- **Asset Categories**: Easy filtering by crypto, forex, stocks, options

## Getting Started

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Run Development Server**:
   ```bash
   npm run dev
   ```

3. **Open Browser**:
   Navigate to `http://localhost:3000`

## Project Structure

```
├── app/
│   ├── components/
│   │   ├── TradingPostCard.tsx    # Main post card component
│   │   ├── CreatePostModal.tsx    # Post creation modal
│   │   └── Icons.tsx              # SVG icon components
│   ├── globals.css                # Global styles and custom utilities
│   ├── layout.tsx                 # Root layout with Whop providers
│   └── page.tsx                   # Main discovery page
├── next.config.js                 # Next.js configuration
├── tailwind.config.js             # Tailwind CSS configuration
└── tsconfig.json                  # TypeScript configuration
```

## Key Components

### TradingPostCard
- Displays trader information with avatar and verification
- Shows trading setup details in organized grid
- Includes chart image with position type overlay
- Social interaction buttons with state management
- Tag display and timeframe indicator

### CreatePostModal
- Comprehensive form for all trading setup fields
- Image upload with preview functionality
- Real-time risk/reward calculation
- Form validation and submission handling
- Clean modal design with proper spacing

### Discovery Page
- Search and filtering functionality
- Responsive grid layout for posts
- Results summary and empty states
- Sorting options for different views
- Integration with create post modal

## Customization

The design system is built to be easily customizable:

- **Colors**: Modify the Tailwind config for brand colors
- **Shadows**: Custom shadow utilities in globals.css
- **Components**: Frosted UI components can be themed
- **Layout**: Responsive breakpoints easily adjustable

## Future Enhancements

- User authentication and profiles
- Real-time updates and notifications
- Advanced filtering (by performance, risk level, etc.)
- Trading performance tracking
- Integration with trading platforms
- Community features (following, groups, etc.)
