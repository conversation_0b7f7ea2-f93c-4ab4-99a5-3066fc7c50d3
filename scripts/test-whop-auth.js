// Test script for Whop authentication integration
// Run with: node scripts/test-whop-auth.js

const { WhopAPI } = require('@whop/api');

async function testWhopAuth() {
  console.log('🧪 Testing Whop Authentication Integration...\n');

  // Test 1: Check environment variables
  console.log('1. Checking environment variables...');
  const requiredEnvVars = [
    'WHOP_API_KEY',
    'NEXT_PUBLIC_WHOP_APP_ID',
    'NEXT_PUBLIC_WHOP_AGENT_USER_ID',
    'NEXT_PUBLIC_WHOP_COMPANY_ID'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing environment variables:', missingVars);
    return;
  }
  console.log('✅ All required environment variables are set\n');

  // Test 2: Initialize Whop API
  console.log('2. Initializing Whop API...');
  try {
    const whop = new WhopAPI({
      apiKey: process.env.WHOP_API_KEY,
    });
    console.log('✅ Whop API initialized successfully\n');

    // Test 3: Test user retrieval
    console.log('3. Testing user retrieval...');
    try {
      const testUserId = process.env.NEXT_PUBLIC_WHOP_AGENT_USER_ID;
      const user = await whop.users.retrieve(testUserId);
      
      console.log('✅ Successfully retrieved user data:');
      console.log(`   - ID: ${user.id}`);
      console.log(`   - Username: ${user.username}`);
      console.log(`   - Email: ${user.email}`);
      console.log(`   - Profile Picture: ${user.profilePictureUrl || 'None'}`);
      console.log(`   - Discord ID: ${user.discordId || 'None'}\n`);
    } catch (error) {
      console.error('❌ Failed to retrieve user:', error.message);
    }

  } catch (error) {
    console.error('❌ Failed to initialize Whop API:', error.message);
    return;
  }

  // Test 4: Test API routes (if server is running)
  console.log('4. Testing API routes...');
  try {
    const testUserId = process.env.NEXT_PUBLIC_WHOP_AGENT_USER_ID;
    const response = await fetch(`http://localhost:3000/api/auth/whop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId: testUserId }),
    });

    if (response.ok) {
      const userData = await response.json();
      console.log('✅ API route working correctly');
      console.log(`   - Retrieved user: ${userData.username}\n`);
    } else {
      console.log('⚠️  API route test skipped (server not running or route not accessible)\n');
    }
  } catch (error) {
    console.log('⚠️  API route test skipped (server not running)\n');
  }

  console.log('🎉 Whop authentication integration test completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Run the migration: Execute supabase/migrations/add_whop_fields.sql in your Supabase dashboard');
  console.log('2. Start your development server: npm run dev');
  console.log('3. Test with Whop URL parameters: ?whop_user_id=USER_ID or ?token=TOKEN');
  console.log('4. Verify user data is synced to Supabase profiles table');
}

// Run the test
testWhopAuth().catch(console.error);
