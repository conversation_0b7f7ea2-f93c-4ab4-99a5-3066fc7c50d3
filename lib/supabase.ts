import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Profile {
  id: string
  username: string
  full_name: string | null
  avatar_url: string | null
  verified: boolean
  followers_count: number
  bio: string | null
  whop_user_id: string | null
  whop_profile_url: string | null
  whop_username: string | null
  whop_city: string | null
  whop_country: string | null
  whop_phone_verified: boolean
  whop_banner_url: string | null
  whop_created_at: string | null
  created_at: string
  updated_at: string
}

export interface TradingPost {
  id: string
  user_id: string
  symbol: string
  position_type: 'Long' | 'Short'
  entry_price: string
  target_price: string
  stop_loss: string
  risk_reward_ratio: string
  timeframe: string
  description: string
  image_url: string | null
  status: 'in_position' | 'target_hit' | 'stopped_out' | 'exited'
  likes_count: number
  comments_count: number
  tags: string[]
  category: 'crypto' | 'forex' | 'stocks' | 'options'
  hidden: boolean
  created_at: string
  updated_at: string
  // Relations
  profile?: Profile
  user_has_liked?: boolean
}

export interface Community {
  id: string
  name: string
  description: string
  image_url: string | null
  member_count: number
  is_private: boolean
  owner_id: string
  tags: string[]
  created_at: string
  updated_at: string
  // Relations
  owner?: Profile
  user_is_member?: boolean
}

export interface PostLike {
  id: string
  user_id: string
  post_id: string
  created_at: string
}

export interface PostComment {
  id: string
  user_id: string
  post_id: string
  content: string
  created_at: string
  updated_at: string
  // Relations
  profile?: Profile
}

export interface CommunityMember {
  id: string
  user_id: string
  community_id: string
  role: 'member' | 'moderator' | 'admin'
  joined_at: string
}

export interface CommunityReview {
  id: string
  user_id: string
  community_id: string
  rating: number
  comment: string
  created_at: string
  // Relations
  profile?: Profile
}
