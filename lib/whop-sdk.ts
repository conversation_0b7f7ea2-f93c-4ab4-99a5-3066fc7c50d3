// Whop SDK setup and configuration
// This file sets up the Whop SDK for server-side operations

import { WhopServerSdk } from '@whop/api';

// Initialize the Whop Server SDK client
export const whopSdk = WhopServerSdk({
  appApiKey: process.env.WHOP_API_KEY!,
  appId: process.env.NEXT_PUBLIC_WHOP_APP_ID!,
});

// Types for Whop user data based on the API documentation
export interface WhopUser {
  id: string;
  name: string;
  username: string;
  email?: string;
  profilePicture?: {
    sourceUrl: string;
  };
  bio?: string;
  phoneVerified?: boolean;
  city?: string;
  country?: string;
  banner?: {
    sourceUrl: string;
  };
  createdAt: number; // Unix timestamp
}

export interface WhopCurrentUser extends WhopUser {
  email: string;
  bannerImage?: string;
  dateOfBirthDay?: number;
  dateOfBirthMonth?: number;
  dateOfBirthYear?: number;
  ledgerAccount?: {
    id: string;
    transferFee?: number;
    balanceCaches?: {
      nodes: Array<{
        balance: number;
        pendingBalance: number;
        currency: string;
      }>;
    };
  };
}

// Helper function to get user profile URL
export function getWhopProfileUrl(userId: string): string {
  return `https://whop.com/@${userId}`;
}

// Helper function to format user data for our application
export function formatWhopUserForApp(whopUser: any): {
  id: string;
  username: string;
  name: string;
  email?: string;
  profilePictureUrl?: string;
  bio?: string;
  createdAt: string;
} {
  return {
    id: whopUser.id,
    username: whopUser.username,
    name: whopUser.name,
    email: whopUser.email || undefined,
    profilePictureUrl: whopUser.profilePicture?.sourceUrl,
    bio: whopUser.bio || undefined,
    createdAt: new Date(whopUser.createdAt * 1000).toISOString(), // Convert Unix timestamp to ISO string
  };
}
