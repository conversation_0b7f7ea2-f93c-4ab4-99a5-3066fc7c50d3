import { supabase } from './supabase'
import type { TradingPost, Profile, PostComment, Community } from './supabase'
import type { WhopUser } from './whop'
import { getWhopProfileUrl } from './whop'

// Profile functions
export async function getProfile(userId: string): Promise<Profile | null> {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return data
}

export async function createProfile(profile: Omit<Profile, 'created_at' | 'updated_at'>): Promise<Profile | null> {
  const { data, error } = await supabase
    .from('profiles')
    .insert(profile)
    .select()
    .single()

  if (error) {
    console.error('Error creating profile:', error)
    return null
  }

  return data
}

// Whop user sync functions
export async function syncWhopUserToSupabase(whopUser: WhopUser, supabaseUserId?: string): Promise<Profile | null> {
  try {
    // First, check if a profile with this Whop user ID already exists
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('whop_user_id', whopUser.id)
      .single()

    if (existingProfile) {
      // Update existing profile with latest Whop data
      const updates = {
        username: whopUser.username,
        full_name: whopUser.name || whopUser.username,
        avatar_url: whopUser.profilePictureUrl || null,
        bio: whopUser.bio || existingProfile.bio, // Keep existing bio if Whop doesn't have one
        whop_profile_url: getWhopProfileUrl(whopUser.id),
        whop_discord_id: null, // Discord ID not available in current API structure
        updated_at: new Date().toISOString()
      }

      const { data: updatedProfile, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('whop_user_id', whopUser.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating existing Whop profile:', error)
        return null
      }

      return updatedProfile
    }

    // Create new profile
    const profileData = {
      id: supabaseUserId || whopUser.id, // Use provided Supabase user ID or Whop ID
      username: whopUser.username,
      full_name: whopUser.name || whopUser.username,
      avatar_url: whopUser.profilePictureUrl || null,
      verified: false,
      followers_count: 0,
      bio: whopUser.bio || null,
      whop_user_id: whopUser.id,
      whop_profile_url: getWhopProfileUrl(whopUser.id),
      whop_discord_id: null // Discord ID not available in current API structure
    }

    const { data: newProfile, error } = await supabase
      .from('profiles')
      .insert(profileData)
      .select()
      .single()

    if (error) {
      console.error('Error creating new Whop profile:', error)
      return null
    }

    return newProfile
  } catch (error) {
    console.error('Error syncing Whop user to Supabase:', error)
    return null
  }
}

export async function getProfileByWhopUserId(whopUserId: string): Promise<Profile | null> {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('whop_user_id', whopUserId)
    .single()

  if (error) {
    console.error('Error fetching profile by Whop user ID:', error)
    return null
  }

  return data
}

export async function updateProfile(userId: string, updates: Partial<Profile>): Promise<boolean> {
  console.log('🔄 Updating profile for user:', userId);
  console.log('📝 Updates:', updates);

  const { data, error } = await supabase
    .from('profiles')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
    .select()

  console.log('📊 Update response - Data:', data);
  console.log('❌ Update response - Error:', error);

  if (error) {
    console.error('Error updating profile:', error)
    return false
  }

  return true
}

// Trading post functions
export async function getTradingPosts(filters?: {
  category?: string
  search?: string
  sortBy?: 'recent' | 'likes' | 'comments'
  limit?: number
}): Promise<TradingPost[]> {
  let query = supabase
    .from('trading_posts')
    .select(`
      *,
      profile:profiles(*)
    `)
    .eq('hidden', false) // Only show non-hidden posts

  // Apply filters
  if (filters?.category && filters.category !== 'all') {
    query = query.eq('category', filters.category)
  }

  if (filters?.search) {
    query = query.or(`symbol.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
  }

  // Apply sorting
  switch (filters?.sortBy) {
    case 'likes':
      query = query.order('likes_count', { ascending: false })
      break
    case 'comments':
      query = query.order('comments_count', { ascending: false })
      break
    default:
      query = query.order('created_at', { ascending: false })
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching trading posts:', error)
    return []
  }

  return data || []
}

export async function createTradingPost(post: Omit<TradingPost, 'id' | 'created_at' | 'updated_at' | 'likes_count' | 'comments_count' | 'profile'>): Promise<TradingPost | null> {
  const { data, error } = await supabase
    .from('trading_posts')
    .insert(post)
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error creating trading post:', error)
    return null
  }

  return data
}

export async function updateTradingPost(postId: string, updates: Partial<TradingPost>, userId?: string): Promise<TradingPost | null> {
  // If userId is provided, check ownership
  if (userId) {
    const { data: post } = await supabase
      .from('trading_posts')
      .select('user_id')
      .eq('id', postId)
      .single()

    if (!post || post.user_id !== userId) {
      console.error('Unauthorized: User does not own this post')
      return null
    }
  }

  const { data, error } = await supabase
    .from('trading_posts')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', postId)
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error updating trading post:', error)
    return null
  }

  return data
}

export async function deleteTradingPost(postId: string, userId?: string): Promise<boolean> {
  // If userId is provided, check ownership
  if (userId) {
    const { data: post } = await supabase
      .from('trading_posts')
      .select('user_id')
      .eq('id', postId)
      .single()

    if (!post || post.user_id !== userId) {
      console.error('Unauthorized: User does not own this post')
      return false
    }
  }

  const { error } = await supabase
    .from('trading_posts')
    .delete()
    .eq('id', postId)

  if (error) {
    console.error('Error deleting trading post:', error)
    return false
  }

  return true
}

// Like functions
export async function togglePostLike(postId: string, userId: string): Promise<boolean> {
  // Check if user already liked the post
  const { data: existingLike } = await supabase
    .from('post_likes')
    .select('id')
    .eq('post_id', postId)
    .eq('user_id', userId)
    .single()

  if (existingLike) {
    // Unlike the post
    const { error } = await supabase
      .from('post_likes')
      .delete()
      .eq('post_id', postId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error removing like:', error)
      return false
    }

    // Decrement likes count
    await supabase.rpc('decrement_likes_count', { post_id: postId })
  } else {
    // Like the post
    const { error } = await supabase
      .from('post_likes')
      .insert({ post_id: postId, user_id: userId })

    if (error) {
      console.error('Error adding like:', error)
      return false
    }

    // Increment likes count
    await supabase.rpc('increment_likes_count', { post_id: postId })
  }

  return true
}

export async function getPostComments(postId: string): Promise<PostComment[]> {
  const { data, error } = await supabase
    .from('post_comments')
    .select(`
      *,
      profile:profiles(*)
    `)
    .eq('post_id', postId)
    .order('created_at', { ascending: true })

  if (error) {
    console.error('Error fetching comments:', error)
    return []
  }

  return data || []
}

export async function createComment(comment: Omit<PostComment, 'id' | 'created_at' | 'updated_at' | 'profile'>): Promise<PostComment | null> {
  const { data, error } = await supabase
    .from('post_comments')
    .insert(comment)
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error creating comment:', error)
    return null
  }

  // Increment comments count
  await supabase.rpc('increment_comments_count', { post_id: comment.post_id })

  return data
}



// Image upload function
export async function uploadImage(file: File, bucket: string = 'trading-images'): Promise<string | null> {
  const fileExt = file.name.split('.').pop()
  const fileName = `${Math.random()}.${fileExt}`
  const filePath = `${fileName}`

  const { error } = await supabase.storage
    .from(bucket)
    .upload(filePath, file)

  if (error) {
    console.error('Error uploading image:', error)
    return null
  }

  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(filePath)

  return data.publicUrl
}
