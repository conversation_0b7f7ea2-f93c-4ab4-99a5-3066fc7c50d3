import { WhopAPI } from '@whop/api';

// Client-side Whop utilities
export interface WhopUser {
  id: string;
  email: string;
  username: string;
  profilePictureUrl?: string;
  discordId?: string;
  createdAt: string;
}

// Get user data from <PERSON><PERSON> using a token
export async function getWhopUser(token: string): Promise<WhopUser | null> {
  try {
    const response = await fetch(`/api/auth/whop?token=${encodeURIComponent(token)}`);
    
    if (!response.ok) {
      console.error('Failed to get Whop user:', response.statusText);
      return null;
    }
    
    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error getting Whop user:', error);
    return null;
  }
}

// Get user data from Whop using user ID (server-side call)
export async function getWhopUserById(userId: string): Promise<WhopUser | null> {
  try {
    const response = await fetch('/api/auth/whop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });
    
    if (!response.ok) {
      console.error('Failed to get Whop user by ID:', response.statusText);
      return null;
    }
    
    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error getting Whop user by ID:', error);
    return null;
  }
}

// Check if we're running in a Whop iframe
export function isInWhopIframe(): boolean {
  try {
    return window.parent !== window && window.location !== window.parent.location;
  } catch (e) {
    return true; // If we can't access parent, we're probably in an iframe
  }
}

// Get Whop context from URL parameters or iframe
export function getWhopContext(): { userId?: string; token?: string } {
  if (typeof window === 'undefined') return {};
  
  const urlParams = new URLSearchParams(window.location.search);
  const userId = urlParams.get('whop_user_id') || urlParams.get('user_id');
  const token = urlParams.get('whop_token') || urlParams.get('token');
  
  return {
    userId: userId || undefined,
    token: token || undefined,
  };
}

// Generate Whop profile URL
export function getWhopProfileUrl(userId: string): string {
  return `https://whop.com/user/${userId}`;
}
