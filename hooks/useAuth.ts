import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { getProfile, createProfile, syncWhopUserToSupabase, getProfileByWhopUserId } from '../lib/database'
import { getCurrentWhopUser, getWhopUserById, isInWhopIframe } from '../lib/whop'
import type { Profile } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    initializeAuth();
  }, [])

  const initializeAuth = async () => {
    try {
      console.log('🔐 Initializing authentication...');

      // First, try to get current user from Whop (this will work in Whop iframe)
      const whopUser = await getCurrentWhopUser();

      if (whopUser) {
        console.log('✅ Found Whop user:', whopUser.username);
        // Sync Whop user to Supabase and create/update profile
        const profile = await syncWhopUserToSupabase(whopUser);

        if (profile) {
          console.log('✅ Synced profile to Supabase:', profile.username);
          // Create a user object compatible with Supabase auth
          const user: User = {
            id: profile.id,
            email: whopUser.email || `${whopUser.username}@whop.local`,
            user_metadata: {
              full_name: whopUser.name,
              avatar_url: whopUser.profilePictureUrl
            },
            app_metadata: {
              whop_user_id: whopUser.id
            },
            aud: 'authenticated',
            created_at: whopUser.createdAt,
            email_confirmed_at: whopUser.createdAt,
            phone: '',
            confirmed_at: whopUser.createdAt,
            last_sign_in_at: new Date().toISOString(),
            role: 'authenticated',
            updated_at: new Date().toISOString()
          };

          setUser(user);
          setProfile(profile);
          setLoading(false);
          return;
        }
      }

      console.log('⚠️ No Whop user found, checking Supabase session...');

      // Fallback: Check for existing Supabase auth session
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        console.log('✅ Found Supabase session for user:', session.user.id);
        setUser(session.user);
        await loadProfile(session.user.id);
      } else {
        console.log('⚠️ No authentication found, creating unique mock user for development');
        // No authentication found, create unique mock user for development
        await createMockUser();
      }
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      await createMockUser();
    }
  }

  const createMockUser = async () => {
    // Create a unique user ID based on browser fingerprint or localStorage
    let userId = localStorage.getItem('mock_user_id');

    if (!userId) {
      // Generate a unique ID for this browser/user
      userId = 'mock_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
      localStorage.setItem('mock_user_id', userId);
    }

    // Get or create a unique username for this mock user
    let username = localStorage.getItem('mock_username');
    if (!username) {
      const adjectives = ['Cool', 'Smart', 'Fast', 'Brave', 'Wise', 'Bold', 'Swift', 'Sharp'];
      const nouns = ['Trader', 'Investor', 'Analyst', 'Expert', 'Pro', 'Master', 'Guru', 'Wizard'];
      const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
      const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
      const randomNumber = Math.floor(Math.random() * 999) + 1;
      username = `${randomAdjective}${randomNoun}${randomNumber}`;
      localStorage.setItem('mock_username', username);
    }

    console.log('🎭 Creating mock user:', { userId, username });

    const mockUser = {
      id: userId,
      email: `${username.toLowerCase()}@mock.local`,
      user_metadata: {
        full_name: username,
        avatar_url: null // No default avatar
      },
      app_metadata: {},
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      email_confirmed_at: new Date().toISOString(),
      phone: '',
      confirmed_at: new Date().toISOString(),
      last_sign_in_at: new Date().toISOString(),
      role: 'authenticated',
      updated_at: new Date().toISOString()
    } as User;

    setUser(mockUser);
    await loadProfile(mockUser.id);
  }

  const loadProfile = async (userId: string) => {
    try {
      console.log('📋 Loading profile for user:', userId);
      let userProfile = await getProfile(userId)

      // If profile doesn't exist, create one
      if (!userProfile) {
        console.log('➕ Creating new profile for user:', userId);

        // Get stored username for mock users
        const storedUsername = localStorage.getItem('mock_username') || 'testuser';
        const isMockUser = userId.startsWith('mock_');

        userProfile = await createProfile({
          id: userId,
          username: isMockUser ? storedUsername : 'testuser',
          full_name: isMockUser ? storedUsername : 'Test User',
          avatar_url: null, // No default avatar - will be handled in UI
          verified: false,
          followers_count: 0,
          bio: isMockUser ? `Mock user for development - ${storedUsername}` : null,
          whop_user_id: null,
          whop_profile_url: null,
          whop_username: null,
          whop_city: null,
          whop_country: null,
          whop_phone_verified: false,
          whop_banner_url: null,
          whop_created_at: null
        })
      }

      console.log('✅ Profile loaded:', userProfile?.username);
      setProfile(userProfile)
    } catch (error) {
      console.error('❌ Error loading profile:', error)
      // Create a fallback profile for testing with unique ID
      const fallbackUsername = localStorage.getItem('mock_username') || 'fallbackuser';
      setProfile({
        id: userId,
        username: fallbackUsername,
        full_name: fallbackUsername,
        avatar_url: null, // No default avatar
        verified: false,
        followers_count: 0,
        bio: 'Fallback user for development',
        whop_user_id: null,
        whop_profile_url: null,
        whop_username: null,
        whop_city: null,
        whop_country: null,
        whop_phone_verified: false,
        whop_banner_url: null,
        whop_created_at: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    return { error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const signInWithProvider = async (provider: 'google' | 'github' | 'discord') => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    return { error }
  }

  return {
    user,
    profile,
    loading,
    loadProfile,
    signIn,
    signUp,
    signOut,
    signInWithProvider,
    isAuthenticated: !!user
  }
}
