import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { getProfile, createProfile, syncWhopUserToSupabase, getProfileByWhopUserId } from '../lib/database'
import { getWhopContext, getWhopUser, getWhopUserById } from '../lib/whop'
import type { Profile } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    initializeAuth();
  }, [])

  const initializeAuth = async () => {
    try {
      // Get Whop context from URL or iframe
      const whopContext = getWhopContext();

      if (whopContext.userId) {
        // We have a Whop user ID, fetch user data
        let whopUser;

        if (whopContext.token) {
          // Use token to get user data
          whopUser = await getWhopUser(whopContext.token);
        } else {
          // Use user ID to get user data
          whopUser = await getWhopUserById(whopContext.userId);
        }

        if (whopUser) {
          // Sync Whop user to Supabase and create/update profile
          const profile = await syncWhopUserToSupabase(whopUser);

          if (profile) {
            // Create a user object compatible with Supabase auth
            const user: User = {
              id: profile.id,
              email: whopUser.email,
              user_metadata: {
                full_name: whopUser.username,
                avatar_url: whopUser.profilePictureUrl
              },
              app_metadata: {
                whop_user_id: whopUser.id
              },
              aud: 'authenticated',
              created_at: whopUser.createdAt,
              email_confirmed_at: whopUser.createdAt,
              phone: '',
              confirmed_at: whopUser.createdAt,
              last_sign_in_at: new Date().toISOString(),
              role: 'authenticated',
              updated_at: new Date().toISOString()
            };

            setUser(user);
            setProfile(profile);
            setLoading(false);
            return;
          }
        }
      }

      // Fallback: Check for existing Supabase auth session
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        setUser(session.user);
        await loadProfile(session.user.id);
      } else {
        // No authentication found, create mock user for development
        await createMockUser();
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      await createMockUser();
    }
  }

  const createMockUser = async () => {
    const mockUser = {
      id: '550e8400-e29b-41d4-a716-446655440000',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test User',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      },
      app_metadata: {},
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      email_confirmed_at: new Date().toISOString(),
      phone: '',
      confirmed_at: new Date().toISOString(),
      last_sign_in_at: new Date().toISOString(),
      role: 'authenticated',
      updated_at: new Date().toISOString()
    } as User;

    setUser(mockUser);
    await loadProfile(mockUser.id);
  }

  const loadProfile = async (userId: string) => {
    try {
      let userProfile = await getProfile(userId)

      // If profile doesn't exist, create one
      if (!userProfile) {
        userProfile = await createProfile({
          id: userId,
          username: 'testuser',
          full_name: 'Test User',
          avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
          verified: false,
          followers_count: 0,
          bio: null,
          whop_user_id: null,
          whop_profile_url: null,
          whop_discord_id: null
        })
      }

      setProfile(userProfile)
    } catch (error) {
      console.error('Error loading profile:', error)
      // Create a fallback profile for testing
      setProfile({
        id: '550e8400-e29b-41d4-a716-446655440000',
        username: 'testuser',
        full_name: 'Test User',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        verified: false,
        followers_count: 0,
        bio: 'Test user for development',
        whop_user_id: null,
        whop_profile_url: null,
        whop_discord_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    return { error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const signInWithProvider = async (provider: 'google' | 'github' | 'discord') => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    return { error }
  }

  return {
    user,
    profile,
    loading,
    loadProfile,
    signIn,
    signUp,
    signOut,
    signInWithProvider,
    isAuthenticated: !!user
  }
}
