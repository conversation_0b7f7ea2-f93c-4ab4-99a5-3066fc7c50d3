"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, Text, Avatar, Badge, Button } from "@whop/react/components";
import { UsersIcon, LockIcon, GlobeIcon, ImageIcon } from "../../components/Icons";

interface Community {
  id: string;
  name: string;
  description: string;
  image: string;
  memberCount: number;
  isPrivate: boolean;
  owner: {
    name: string;
    avatar: string;
  };
  tags: string[];
  lastActivity: string;
  isJoined: boolean;
}

interface CommunityCardProps {
  community: Community;
}

export function CommunityCard({ community }: CommunityCardProps) {
  const router = useRouter();
  const [isJoined, setIsJoined] = useState(community.isJoined);

  const handleJoinToggle = () => {
    setIsJoined(!isJoined);
  };

  const handleCommunityClick = () => {
    router.push(`/community/${community.id}`);
  };

  const [showReviews, setShowReviews] = useState(false);

  // Mock reviews data
  const reviews = [
    { id: 1, user: "TraderMike", rating: 5, comment: "Amazing community with great signals!", time: "2d ago" },
    { id: 2, user: "CryptoSarah", rating: 4, comment: "Very helpful for beginners. Active community.", time: "1w ago" },
    { id: 3, user: "ForexPro", rating: 5, comment: "Best trading community I've joined. Highly recommend!", time: "2w ago" }
  ];

  const averageRating = reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length;

  return (
    <Card
      className="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-300 cursor-pointer"
      style={{borderWidth: '1px', backgroundColor: 'white', boxShadow: 'none'}}
      onClick={handleCommunityClick}
    >
      <div className="p-4">
        {/* Header Row */}
        <div className="flex items-center gap-3 mb-3">
          {/* Community Image */}
          <div className="w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex-shrink-0">
            {community.image ? (
              <img
                src={community.image}
                alt={community.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <ImageIcon className="w-5 h-5 text-white/70" />
              </div>
            )}
          </div>

          {/* Community Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-body font-semibold text-gray-900 truncate">{community.name}</h3>
              <span className={`px-2 py-0.5 rounded-md text-xs font-medium ${
                community.isPrivate
                  ? 'bg-red-100 text-red-700'
                  : 'bg-green-100 text-green-700'
              }`}>
                {community.isPrivate ? 'Private' : 'Public'}
              </span>
            </div>
            <div className="flex items-center gap-3 text-caption text-gray-500">
              <div className="flex items-center gap-1">
                <UsersIcon className="w-3 h-3" />
                {community.memberCount.toLocaleString()}
              </div>
              <span>•</span>
              <span>{community.lastActivity}</span>
            </div>
          </div>

          {/* Join Status */}
          <div className="flex-shrink-0">
            {isJoined ? (
              <span className="text-caption text-green-600 font-medium">Joined</span>
            ) : (
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  handleJoinToggle();
                }}
                className="bg-blue-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-blue-700 transition-colors"
                style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
              >
                Join
              </Button>
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-caption text-gray-600 mb-3 line-clamp-2">
          {community.description}
        </p>

        {/* Reviews Section */}
        <div className="border-t border-gray-100 pt-3">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowReviews(!showReviews);
            }}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className={`w-3 h-3 ${i < Math.floor(averageRating) ? 'text-yellow-400' : 'text-gray-300'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="text-caption text-gray-600">
                {averageRating.toFixed(1)} ({reviews.length} reviews)
              </span>
            </div>
            <svg
              className={`w-4 h-4 text-gray-400 transition-transform ${showReviews ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {/* Reviews Dropdown */}
          {showReviews && (
            <div className="mt-3 space-y-3 max-h-48 overflow-y-auto">
              {reviews.map((review) => (
                <div key={review.id} className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-caption font-medium text-gray-900">{review.user}</span>
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-2.5 h-2.5 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                    <span className="text-caption text-gray-500">{review.time}</span>
                  </div>
                  <p className="text-caption text-gray-700">{review.comment}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
