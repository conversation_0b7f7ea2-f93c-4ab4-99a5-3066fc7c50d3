"use client";

import { useState } from "react";
import { Button, Text } from "@whop/react/components";

interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (postData: any) => Promise<boolean>;
}

export function CreatePostModal({ isOpen, onClose, onSubmit }: CreatePostModalProps) {
  const [formData, setFormData] = useState({
    symbol: "",
    type: "Long",
    entry: "",
    target: "",
    stopLoss: "",
    timeframe: "4H",
    description: "",
    tags: "",
    status: "in_position",
    image: null as File | null
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const calculateRiskReward = () => {
    if (!formData.entry || !formData.target || !formData.stopLoss) return "N/A";
    
    const entry = parseFloat(formData.entry.replace(/[^0-9.-]/g, ''));
    const target = parseFloat(formData.target.replace(/[^0-9.-]/g, ''));
    const stopLoss = parseFloat(formData.stopLoss.replace(/[^0-9.-]/g, ''));
    
    if (isNaN(entry) || isNaN(target) || isNaN(stopLoss)) return "N/A";
    
    const isLong = formData.type === "Long";
    const risk = Math.abs(entry - stopLoss);
    const reward = Math.abs(target - entry);
    
    if (risk === 0) return "N/A";
    
    const ratio = reward / risk;
    return `1:${ratio.toFixed(1)}`;
  };

  const handleSubmit = async () => {
    if (onSubmit) {
      setIsSubmitting(true);

      // Determine category based on symbol
      let category = "crypto";
      const symbol = formData.symbol.toUpperCase();
      if (symbol.includes("USD") || symbol.includes("EUR") || symbol.includes("GBP") || symbol.includes("JPY")) {
        category = "forex";
      } else if (symbol.includes("SPY") || symbol.includes("QQQ") || symbol.includes("AAPL") || symbol.includes("TSLA")) {
        category = "stocks";
      } else if (symbol.includes("C") || symbol.includes("P") || symbol.includes("CALL") || symbol.includes("PUT")) {
        category = "options";
      }

      const success = await onSubmit({
        ...formData,
        category
      });

      setIsSubmitting(false);

      if (success) {
        // Show success state
        setShowSuccess(true);

        // Reset form after a delay
        setTimeout(() => {
          setFormData({
            symbol: "",
            type: "Long",
            entry: "",
            target: "",
            stopLoss: "",
            timeframe: "4H",
            description: "",
            tags: "",
            status: "in_position",
            image: null
          });
          setImagePreview(null);
          setShowSuccess(false);
          onClose();
        }, 2000);
      }
    } else {
      // Fallback for when no onSubmit is provided
      console.log("Submitting post:", formData);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto bg-white rounded-lg border border-gray-200" style={{borderRadius: '8px'}}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900">
            Create Trading Post
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            ×
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Image Upload */}
          <div>
            <h3 className="text-h3 text-gray-900 mb-3">
              Chart Image
            </h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              {imagePreview ? (
                <div className="relative">
                  <img 
                    src={imagePreview} 
                    alt="Preview" 
                    className="max-h-48 mx-auto rounded-lg"
                  />
                  <Button
                    variant="ghost"
                    size="1"
                    onClick={() => {
                      setImagePreview(null);
                      setFormData(prev => ({ ...prev, image: null }));
                    }}
                    className="absolute top-2 right-2 bg-black/50 text-white hover:bg-black/70 rounded-full p-1"
                  >
                    ×
                  </Button>
                </div>
              ) : (
                <label className="cursor-pointer">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-3">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <Text size="3" className="text-gray-600 mb-1">
                      Upload chart image
                    </Text>
                    <Text size="2" className="text-gray-400">
                      PNG, JPG up to 10MB
                    </Text>
                  </div>
                </label>
              )}
            </div>
          </div>

          {/* Trading Setup Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Symbol
              </h3>
              <input
                type="text"
                placeholder="e.g., BTC/USDT"
                value={formData.symbol}
                onChange={(e) => handleInputChange("symbol", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Position Type
              </h3>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange("type", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              >
                <option value="Long">Long</option>
                <option value="Short">Short</option>
              </select>
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Position Status
              </h3>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              >
                <option value="in_position">In Position</option>
                <option value="target_hit">Target Hit</option>
                <option value="stopped_out">Stopped Out</option>
                <option value="exited">Exited</option>
              </select>
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Entry Price
              </h3>
              <input
                type="text"
                placeholder="e.g., $42,350"
                value={formData.entry}
                onChange={(e) => handleInputChange("entry", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Target Price
              </h3>
              <input
                type="text"
                placeholder="e.g., $45,200"
                value={formData.target}
                onChange={(e) => handleInputChange("target", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Stop Loss
              </h3>
              <input
                type="text"
                placeholder="e.g., $40,800"
                value={formData.stopLoss}
                onChange={(e) => handleInputChange("stopLoss", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Timeframe
              </h3>
              <select
                value={formData.timeframe}
                onChange={(e) => handleInputChange("timeframe", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              >
                <option value="1M">1 Minute</option>
                <option value="5M">5 Minutes</option>
                <option value="15M">15 Minutes</option>
                <option value="1H">1 Hour</option>
                <option value="4H">4 Hours</option>
                <option value="1D">1 Day</option>
                <option value="1W">1 Week</option>
              </select>
            </div>
          </div>

          {/* Risk/Reward Display */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <p className="text-sm text-blue-600 mb-1">
              Risk/Reward Ratio
            </p>
            <p className="text-lg font-bold text-blue-900">
              {calculateRiskReward()}
            </p>
          </div>

          {/* Description */}
          <div>
            <h3 className="text-body font-medium text-gray-900 mb-2">
              Analysis & Description
            </h3>
            <textarea
              placeholder="Explain your trading setup, analysis, and reasoning..."
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={4}
              className="w-full bg-gray-50 border border-gray-200 rounded-lg resize-none px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
              style={{borderWidth: '1px'}}
            />
          </div>

          {/* Tags */}
          <div>
            <h3 className="text-body font-medium text-gray-900 mb-2">
              Tags
            </h3>
            <input
              type="text"
              placeholder="e.g., Bitcoin, Crypto, Swing Trade (comma separated)"
              value={formData.tags}
              onChange={(e) => handleInputChange("tags", e.target.value)}
              className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
              style={{borderWidth: '1px'}}
            />
          </div>
        </div>

        {/* Success State */}
        {showSuccess && (
          <div className="absolute inset-0 bg-white bg-opacity-95 flex items-center justify-center rounded-xl">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-h3 text-gray-900 mb-2">Trading Setup Posted!</h3>
              <p className="text-body text-gray-600">Your trading setup has been successfully shared with the community.</p>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-100">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md font-medium disabled:opacity-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium disabled:opacity-50 transition-colors"
            style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
          >
            {isSubmitting ? 'Posting...' : 'Post Setup'}
          </button>
        </div>
      </div>
    </div>
  );
}
