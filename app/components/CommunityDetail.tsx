"use client";

import { useState } from "react";
import { Card, Text, <PERSON><PERSON>, Bad<PERSON>, But<PERSON> } from "@whop/react/components";
import { UsersIcon, HashIcon, BellIcon, SettingsIcon, PlusIcon, ImageIcon } from "../../components/Icons";

interface CommunityDetailProps {
  communityId: string;
}

export function CommunityDetail({ communityId }: CommunityDetailProps) {
  const [activeChannel, setActiveChannel] = useState("general");
  const [showCreateChannel, setShowCreateChannel] = useState(false);

  // Mock community data
  const community = {
    id: communityId,
    name: "Crypto Traders Hub",
    description: "A community for crypto traders to share insights, strategies, and market analysis.",
    image: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=400&h=200&fit=crop",
    memberCount: 1247,
    isPrivate: false,
    owner: {
      name: "CryptoKing",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
    }
  };

  const channels = [
    { id: "general", name: "general", description: "General discussion", messageCount: 234 },
    { id: "trading-signals", name: "trading-signals", description: "Share trading signals", messageCount: 89 },
    { id: "market-analysis", name: "market-analysis", description: "Market analysis and insights", messageCount: 156 },
    { id: "resources", name: "resources", description: "Educational resources", messageCount: 45 }
  ];

  const recentMessages = [
    {
      id: "1",
      user: { name: "TraderJoe", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" },
      content: "BTC looking bullish on the 4H chart. Anyone else seeing this pattern?",
      timestamp: "2m ago",
      channel: "general"
    },
    {
      id: "2",
      user: { name: "CryptoSarah", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" },
      content: "Just posted a new analysis on ETH/USD in #market-analysis",
      timestamp: "5m ago",
      channel: "general"
    },
    {
      id: "3",
      user: { name: "BlockchainBob", avatar: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=32&h=32&fit=crop&crop=face" },
      content: "Long BTC at 42,350. Target 45,200. SL 40,800",
      timestamp: "8m ago",
      channel: "trading-signals"
    }
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar - Channels */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Community Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center gap-3 mb-3">
            <img 
              src={community.image} 
              alt={community.name}
              className="w-12 h-12 rounded-lg object-cover"
            />
            <div className="flex-1">
              <h2 className="text-h3 font-bold text-gray-900">{community.name}</h2>
              <div className="flex items-center gap-1 text-caption text-gray-500">
                <UsersIcon className="w-3 h-3" />
                {community.memberCount.toLocaleString()} members
              </div>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button size="1" className="flex-1 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-lg text-xs">
              <BellIcon className="w-3 h-3 mr-1" />
              Notifications
            </Button>
            <Button size="1" className="bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-lg p-2">
              <SettingsIcon className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* Channels List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-body font-semibold text-gray-900 uppercase tracking-wide">Channels</h3>
              <Button 
                size="1" 
                onClick={() => setShowCreateChannel(true)}
                className="text-gray-400 hover:text-gray-600 p-1 rounded"
              >
                <PlusIcon className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="space-y-1">
              {channels.map((channel) => (
                <button
                  key={channel.id}
                  onClick={() => setActiveChannel(channel.id)}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    activeChannel === channel.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <HashIcon className="w-4 h-4" />
                    <span className="text-body font-medium">{channel.name}</span>
                  </div>
                  <p className="text-caption text-gray-500 ml-6 mt-0.5">
                    {channel.description}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Chat */}
      <div className="flex-1 flex flex-col">
        {/* Channel Header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center gap-2">
            <HashIcon className="w-5 h-5 text-gray-400" />
            <h1 className="text-h2 font-bold text-gray-900">
              {channels.find(c => c.id === activeChannel)?.name}
            </h1>
            <Badge className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-md text-xs">
              {channels.find(c => c.id === activeChannel)?.messageCount} messages
            </Badge>
          </div>
          <p className="text-body text-gray-600 mt-1">
            {channels.find(c => c.id === activeChannel)?.description}
          </p>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {recentMessages
            .filter(msg => msg.channel === activeChannel || activeChannel === "general")
            .map((message) => (
            <div key={message.id} className="flex gap-3">
              <Avatar
                src={message.user.avatar}
                fallback={message.user.name[0]}
                size="2"
                className="ring-2 ring-gray-100"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-body font-semibold text-gray-900">
                    {message.user.name}
                  </span>
                  <span className="text-caption text-gray-500">
                    {message.timestamp}
                  </span>
                </div>
                <p className="text-body text-gray-700">
                  {message.content}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Message Input */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex gap-3">
            <div className="flex-1">
              <input
                type="text"
                placeholder={`Message #${channels.find(c => c.id === activeChannel)?.name}`}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>
            <Button 
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium"
              style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
            >
              Send
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
