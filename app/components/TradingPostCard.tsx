"use client";

import { useState, useEffect, useRef } from "react";
import { Text, Button } from "@whop/react/components";
import { useTradingPosts } from "../../components/TradingPostsProvider";
import { useAuth } from "../../hooks/useAuth";
import type { TradingPost } from "../../lib/supabase";

interface TradingPostCardProps {
  post: TradingPost;
}

export function TradingPostCard({ post }: TradingPostCardProps) {
  const [isLiked, setIsLiked] = useState(post.user_has_liked || false);
  const [likesCount, setLikesCount] = useState(post.likes_count);
  const [showComments, setShowComments] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(post.status);
  const [showStatusEdit, setShowStatusEdit] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [comments, setComments] = useState<any[]>([]);
  const [showManageMenu, setShowManageMenu] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedDescription, setEditedDescription] = useState(post.description);
  const statusDropdownRef = useRef<HTMLDivElement>(null);
  const manageMenuRef = useRef<HTMLDivElement>(null);

  const { toggleLike, getComments, addComment, refreshPosts, updatePost, deletePost } = useTradingPosts();
  const { user } = useAuth();

  // Check if current user is the post owner
  const isOwner = user?.id === post.user_id;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
        setShowStatusEdit(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Load comments when comments section is opened
  useEffect(() => {
    if (showComments && comments.length === 0) {
      loadComments();
    }
  }, [showComments]);

  // Close management menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (manageMenuRef.current && !manageMenuRef.current.contains(event.target as Node)) {
        setShowManageMenu(false);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
        setShowStatusEdit(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadComments = async () => {
    try {
      const fetchedComments = await getComments(post.id);
      setComments(fetchedComments);
    } catch (error) {
      console.error('Error loading comments:', error);
    }
  };

  const handleLike = async () => {
    if (!user) return;

    setIsLiked(!isLiked);
    setLikesCount(prev => isLiked ? prev - 1 : prev + 1);

    try {
      await toggleLike(post.id);
    } catch (error) {
      // Revert on error
      setIsLiked(isLiked);
      setLikesCount(prev => isLiked ? prev + 1 : prev - 1);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !user) return;

    const commentText = newComment;
    setNewComment(''); // Clear input immediately

    try {
      const success = await addComment(post.id, commentText);
      if (success) {
        // Reload comments to get the latest from database
        await loadComments();
      } else {
        // Restore comment text if failed
        setNewComment(commentText);
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
      // Restore comment text if failed
      setNewComment(commentText);
    }
  };

  const handleEditPost = () => {
    setIsEditing(true);
    setShowManageMenu(false);
  };

  const handleSaveEdit = async () => {
    if (!isOwner) return;

    try {
      const success = await updatePost(post.id, { description: editedDescription });
      if (success) {
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error updating post:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditedDescription(post.description);
    setIsEditing(false);
  };

  const handleDeletePost = async () => {
    if (!isOwner || !confirm('Are you sure you want to delete this post?')) return;

    try {
      const success = await deletePost(post.id);
      if (!success) {
        alert('Failed to delete post. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      alert('Failed to delete post. Please try again.');
    }
  };

  const handleHidePost = async () => {
    if (!isOwner) return;

    try {
      const success = await updatePost(post.id, { hidden: true });
      if (!success) {
        alert('Failed to hide post. Please try again.');
      }
    } catch (error) {
      console.error('Error hiding post:', error);
      alert('Failed to hide post. Please try again.');
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "in_position":
        return { label: "In Position", bgColor: "bg-blue-500", textColor: "text-blue-700" };
      case "target_hit":
        return { label: "Target Hit", bgColor: "bg-green-500", textColor: "text-green-700" };
      case "stopped_out":
        return { label: "Stopped Out", bgColor: "bg-red-500", textColor: "text-red-700" };
      case "exited":
        return { label: "Exited", bgColor: "bg-gray-500", textColor: "text-gray-700" };
      default:
        return { label: "In Position", bgColor: "bg-blue-500", textColor: "text-blue-700" };
    }
  };

  const statusConfig = getStatusConfig(currentStatus);

  const isLongPosition = post.position_type === "Long";

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4" style={{borderRadius: '8px'}}>
      {/* Header */}
      <div className="p-4 pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
              {post.profile?.avatar_url ? (
                <img
                  src={post.profile.avatar_url}
                  alt={post.profile.username || 'User'}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-sm font-medium text-gray-600">
                  {post.profile?.username?.[0] || 'U'}
                </span>
              )}
            </div>
            <div>
              <div className="flex items-center gap-1.5">
                <h3 className="text-body font-semibold text-gray-900">
                  {post.profile?.username || 'Anonymous'}
                </h3>
                {post.profile?.verified && (
                  <Text size="1" color="blue">✓</Text>
                )}
              </div>
              <p className="text-caption">
                {post.profile?.followers_count?.toLocaleString() || 0} followers • {new Date(post.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Management Menu - Only for post owner */}
            {isOwner && (
              <div className="relative" ref={manageMenuRef}>
                <button
                  onClick={() => setShowManageMenu(!showManageMenu)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-md transition-colors"
                  title="Manage post"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>

                {showManageMenu && (
                  <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
                    <button
                      onClick={handleEditPost}
                      className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Edit
                    </button>
                    <button
                      onClick={handleHidePost}
                      className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Hide
                    </button>
                    <button
                      onClick={handleDeletePost}
                      className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Status Button */}
            <div className="relative" ref={statusDropdownRef}>
              <button
                onClick={() => setShowStatusEdit(!showStatusEdit)}
                className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 ${statusConfig.bgColor} bg-opacity-20 ${statusConfig.textColor} hover:bg-opacity-30`}
              >
                {statusConfig.label}
              </button>

              {/* Status Dropdown */}
              {showStatusEdit && (
                <div className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-32">
                  {(["in_position", "target_hit", "stopped_out", "exited"] as const).map((status) => {
                    const config = getStatusConfig(status);
                    return (
                      <button
                        key={status}
                        onClick={() => {
                          setCurrentStatus(status);
                          setShowStatusEdit(false);
                        }}
                        className={`w-full text-left px-3 py-2 text-xs font-medium hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg ${config.textColor}`}
                      >
                        {config.label}
                      </button>
                    );
                  })}
                </div>
              )}
            </div>


          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 pb-3">
        <div className="flex items-center gap-2 mb-3">
          <span
            className={`px-3 py-1 rounded-lg text-xs font-medium ${
              isLongPosition
                ? 'bg-green-500 bg-opacity-20 text-green-700'
                : 'bg-red-500 bg-opacity-20 text-red-700'
            }`}
          >
            {post.position_type}
          </span>
          <h2 className="text-h3 font-bold text-gray-900">{post.symbol}</h2>
          <span className="text-caption bg-gray-100 px-2 py-0.5 rounded-md">
            {post.timeframe}
          </span>
        </div>

        {isEditing ? (
          <div className="mb-4">
            <textarea
              value={editedDescription}
              onChange={(e) => setEditedDescription(e.target.value)}
              className="w-full bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-gray-900 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors resize-none"
              rows={3}
              placeholder="Update your trading analysis..."
            />
            <div className="flex justify-end gap-2 mt-2">
              <button
                onClick={handleCancelEdit}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        ) : (
          <p className="text-body text-gray-700 mb-4 leading-relaxed">
            {post.description}
          </p>
        )}
      </div>

      {/* Chart/Image */}
      <div className="px-4 pb-4">
        <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center" style={{borderRadius: '8px'}}>
          <img
            src={post.image_url || 'https://picsum.photos/400/300?random=1'}
            alt="Trading chart"
            className="w-full h-full object-cover rounded-lg"
            style={{borderRadius: '8px'}}
          />
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-4 pb-4">

        {/* Tags */}
        <div className="flex gap-2 flex-wrap mb-4">
          {post.tags.map((tag, index) => (
            <span
              key={index}
              className="bg-gray-100 text-gray-600 px-2.5 py-1 rounded-full text-xs font-medium"
            >
              #{tag}
            </span>
          ))}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-6">
            <button
              onClick={handleLike}
              className={`flex items-center gap-2 transition-all duration-200 ${
                isLiked
                  ? 'text-red-500'
                  : 'text-gray-500 hover:text-red-500'
              }`}
            >
              <svg className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span className="text-body font-medium">
                {likesCount}
              </span>
            </button>

            <button
              onClick={() => setShowComments(!showComments)}
              className="flex items-center gap-2 text-gray-500 hover:text-blue-500 transition-all duration-200"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span className="text-body font-medium">
                {post.comments_count}
              </span>
            </button>
          </div>

          <div className="flex items-center gap-2 text-caption">
            <span>Entry: {post.entry_price}</span>
            <span>•</span>
            <span>R:R {post.risk_reward_ratio}</span>
          </div>
        </div>

        {/* Comments Section */}
        {showComments && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            {/* Comment Input */}
            <div className="flex gap-3 mb-4">
              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face"
                  alt="User"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1 flex gap-2">
                <input
                  type="text"
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSubmitComment()}
                  className="flex-1 bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                  style={{borderWidth: '1px'}}
                />
                <button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim()}
                  className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-md text-sm font-medium transition-colors"
                >
                  Send
                </button>
              </div>
            </div>

            {/* Comments */}
            <div className="space-y-3">
              {comments.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">No comments yet. Be the first to comment!</p>
              ) : (
                comments.map((comment) => (
                  <div key={comment.id} className="flex gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                      {comment.profile?.avatar_url ? (
                        <img
                          src={comment.profile.avatar_url}
                          alt="User"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-xs font-medium text-gray-600">
                          {comment.profile?.username?.[0] || 'U'}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-semibold text-gray-900">{comment.profile?.username || 'Anonymous'}</span>
                        <span className="text-xs text-gray-500">
                          {new Date(comment.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700">{comment.content}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
