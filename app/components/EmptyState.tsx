"use client";

import { But<PERSON>, Text } from "@whop/react/components";

interface EmptyStateProps {
  onCreatePost: () => void;
  hasFilters: boolean;
}

export function EmptyState({ onCreatePost, hasFilters }: EmptyStateProps) {
  if (hasFilters) {
    return (
      <div className="text-center py-16">
        <h3 className="text-h3 text-gray-500 mb-2">
          No trading setups found
        </h3>
        <p className="text-caption text-gray-400 mb-4">
          Try adjusting your search or filter criteria to find more trading setups.
        </p>
        <Button
          onClick={onCreatePost}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
          style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
        >
          Create Your Own Setup
        </Button>
      </div>
    );
  }

  return (
    <div className="text-center py-20">
      <h2 className="text-h2 text-gray-900 mb-3">
        Welcome to Trading Hub
      </h2>
      <p className="text-body text-gray-600 mb-6 max-w-md mx-auto">
        Share your trading setups, discover new strategies, and connect with fellow traders.
      </p>
      <Button
        onClick={onCreatePost}
        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200"
        style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
      >
        Share Your First Trading Setup
      </Button>
    </div>
  );
}
