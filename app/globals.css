@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  letter-spacing: -0.01em;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Typography System */
.text-h1 {
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.text-h2 {
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.3;
}

.text-h3 {
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.015em;
  line-height: 1.4;
}

.text-body {
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: -0.01em;
  line-height: 1.5;
}

.text-caption {
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: -0.005em;
  line-height: 1.4;
  color: #6b7280;
}

/* Custom styles for clean white theme */
.card-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card-shadow-hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.inner-shadow {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

.inner-shadow-medium {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

/* Override Whop/Frosted UI Card styling to fix dark corners */
.fui-Card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: none !important;
}

.fui-Card::after {
  box-shadow: none !important;
  border: none !important;
}

.fui-Card:hover {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* Ensure clean white background for all card variants */
.fui-Card.fui-variant-surface,
.fui-Card.fui-variant-classic,
.fui-Card.fui-variant-ghost {
  background: white !important;
  border: 1px solid #e5e7eb !important;
}

/* Remove any dark shadows from card pseudo-elements */
.fui-Card.fui-variant-classic::after {
  box-shadow: none !important;
}

/* Clean card inner styling */
.fui-CardInner {
  background: transparent !important;
}
