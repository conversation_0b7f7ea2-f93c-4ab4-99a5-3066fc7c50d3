import { redirect } from "next/navigation";

interface CatchAllPageProps {
  params: {
    slug?: string[];
  };
}

export default function CatchAllPage({ params }: CatchAllPageProps) {
  // Log the attempted path for debugging (optional)
  const attemptedPath = params.slug ? `/${params.slug.join("/")}` : "/";

  // Redirect all unmatched routes to the main page
  // This should work for most cases in Whop
  redirect("/");
}

// Alternative approach: If redirect doesn't work, uncomment this and comment out the redirect above
/*
import DiscoveryPage from "../page";

export default function CatchAllPage({ params }: CatchAllPageProps) {
  // Instead of redirecting, render the main page content directly
  // This ensures the app always shows something useful
  return <DiscoveryPage />;
}
*/
