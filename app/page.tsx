"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { <PERSON><PERSON>, Text, Badge, TextField, Select, Tabs } from "@whop/react/components";
import { TradingPostCard } from "./components/TradingPostCard";
import { CreatePostModal } from "./components/CreatePostModal";
import { EmptyState } from "./components/EmptyState";
import { useTradingPosts } from "../components/TradingPostsProvider";
import { useAuth } from "../hooks/useAuth";

// Inner component that uses the trading posts context
function DiscoveryPageContent() {
  const searchParams = useSearchParams();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [sortBy, setSortBy] = useState("recent");
  const [activeTab, setActiveTab] = useState("discovery");
  
  // Get data from context
  const { posts, loading, createPost, filterPosts } = useTradingPosts();
  const { isAuthenticated } = useAuth();

  // Handle URL parameters for tab switching
  useEffect(() => {
    const tab = searchParams.get('tab');
    // Communities tab is disabled for now
    // if (tab === 'communities') {
    //   setActiveTab('communities');
    // }
  }, [searchParams]);

  // Update filters when they change
  useEffect(() => {
    filterPosts({
      category: selectedFilter,
      search: searchQuery,
      sortBy: sortBy as 'recent' | 'likes' | 'comments'
    });
  }, [searchQuery, selectedFilter, sortBy]);

  const handleCreatePost = async (postData: any) => {
    const success = await createPost(postData);
    if (success) {
      setIsCreateModalOpen(false);
    }
    return success;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 sticky top-0 z-40 backdrop-blur-md bg-white/95">
        <div className="max-w-2xl mx-auto px-6">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-h2 text-gray-900">Trading Hub</h1>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200"
                style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
              >
                Post
              </button>
              <a
                href="/settings"
                className="text-gray-600 hover:text-gray-800 p-1.5 rounded-md transition-colors"
                title="Account Settings"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </a>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex space-x-8 border-b border-gray-100">
            <button
              onClick={() => setActiveTab("discovery")}
              className={`pb-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === "discovery"
                  ? "border-blue-600 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700"
              }`}
            >
              Discovery
            </button>
            <button
              onClick={() => {}} // Disabled for now
              className={`pb-3 text-sm font-medium border-b-2 transition-colors cursor-not-allowed ${
                activeTab === "communities"
                  ? "border-blue-600 text-blue-600"
                  : "border-transparent text-gray-400 hover:text-gray-400"
              }`}
              disabled
            >
              Communities (Coming Soon)
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto px-6 py-6">
        {/* Search and Filters */}
        <div className="mb-6 space-y-3">
          {/* Search Bar */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search by symbol, description, tags, or trader..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
              style={{borderWidth: '1px'}}
            />
          </div>

          {/* Filter and Sort Controls */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="bg-gray-50 border border-gray-200 rounded-md px-3 py-1.5 text-sm text-gray-900 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              >
                <option value="all">All Categories</option>
                <option value="crypto">Crypto</option>
                <option value="forex">Forex</option>
                <option value="stocks">Stocks</option>
                <option value="options">Options</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-gray-50 border border-gray-200 rounded-md px-3 py-1.5 text-sm text-gray-900 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              >
                <option value="recent">Most Recent</option>
                <option value="likes">Most Liked</option>
                <option value="comments">Most Discussed</option>
              </select>
            </div>

            {/* Results Summary */}
            <div className="text-xs text-gray-500">
              {loading ? 'Loading...' : `${posts.length} setups found`}
              {selectedFilter !== "all" && ` in ${selectedFilter}`}
              {searchQuery && ` matching "${searchQuery}"`}
            </div>
          </div>
        </div>

        {/* Content - Always show discovery since communities is disabled */}
        {loading ? (
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-caption mt-4">Loading trading setups...</p>
          </div>
        ) : posts.length > 0 ? (
          <div className="space-y-6">
            {posts.map((post) => (
              <TradingPostCard key={post.id} post={post} />
            ))}
          </div>
        ) : (
          <EmptyState
            onCreatePost={() => setIsCreateModalOpen(true)}
            hasFilters={!!(searchQuery || selectedFilter !== "all")}
          />
        )}
      </div>

      {/* Create Post Modal */}
      <CreatePostModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreatePost}
      />
    </div>
  );
}

// Main component wrapper
export default function DiscoveryPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-caption mt-4">Loading...</p>
        </div>
      </div>
    }>
      <DiscoveryPageContent />
    </Suspense>
  );
}
