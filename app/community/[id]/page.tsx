"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, Text, Avatar, Badge, Button } from "@whop/react/components";
import {
  UsersIcon,
  HashIcon,
  BellIcon,
  SettingsIcon,
  PlusIcon,
  ImageIcon,
  XIcon,
  ArrowLeftIcon
} from "../../../components/Icons";

interface CommunityPageProps {
  params: {
    id: string;
  };
}

export default function CommunityPage({ params }: CommunityPageProps) {
  const router = useRouter();
  const [activeChannel, setActiveChannel] = useState("general");
  const [showCreateChannel, setShowCreateChannel] = useState(false);
  const [newChannelName, setNewChannelName] = useState("");
  const [newChannelDescription, setNewChannelDescription] = useState("");
  const [messageInput, setMessageInput] = useState("");
  const [showAIChat, setShowAIChat] = useState(false);
  const [aiQuestion, setAiQuestion] = useState("");

  // Mock community data - in real app this would come from Supabase
  const community = {
    id: params.id,
    name: "Crypto Traders Hub",
    description: "A community for crypto traders to share insights, strategies, and market analysis.",
    image: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=400&h=200&fit=crop",
    memberCount: 1247,
    isPrivate: false,
    owner: {
      name: "CryptoKing",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
    }
  };

  const [channels, setChannels] = useState([
    { id: "ai-assistant", name: "ai-assistant", description: "Ask AI about community updates & insights", messageCount: 0, isAI: true },
    { id: "general", name: "general", description: "General discussion", messageCount: 234 },
    { id: "trading-signals", name: "trading-signals", description: "Share trading signals", messageCount: 89 },
    { id: "market-analysis", name: "market-analysis", description: "Market analysis and insights", messageCount: 156 },
    { id: "resources", name: "resources", description: "Educational resources", messageCount: 45 }
  ]);

  const [messages, setMessages] = useState([
    {
      id: "1",
      user: { name: "TraderJoe", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" },
      content: "BTC looking bullish on the 4H chart. Anyone else seeing this pattern?",
      timestamp: "2m ago",
      channel: "general"
    },
    {
      id: "2",
      user: { name: "CryptoSarah", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" },
      content: "Just posted a new analysis on ETH/USD in #market-analysis",
      timestamp: "5m ago",
      channel: "general"
    },
    {
      id: "3",
      user: { name: "BlockchainBob", avatar: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=32&h=32&fit=crop&crop=face" },
      content: "Long BTC at 42,350. Target 45,200. SL 40,800",
      timestamp: "8m ago",
      channel: "trading-signals"
    },
    {
      id: "4",
      user: { name: "CryptoKing", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" },
      content: "Welcome everyone! Feel free to share your trading ideas and ask questions.",
      timestamp: "1h ago",
      channel: "general"
    }
  ]);

  const handleCreateChannel = () => {
    if (newChannelName.trim()) {
      const newChannel = {
        id: newChannelName.toLowerCase().replace(/\s+/g, '-'),
        name: newChannelName.toLowerCase().replace(/\s+/g, '-'),
        description: newChannelDescription || "New channel",
        messageCount: 0
      };
      setChannels([...channels, newChannel]);
      setNewChannelName("");
      setNewChannelDescription("");
      setShowCreateChannel(false);
    }
  };

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      const newMessage = {
        id: Date.now().toString(),
        user: { 
          name: "You", 
          avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
        },
        content: messageInput,
        timestamp: "now",
        channel: activeChannel
      };
      setMessages([...messages, newMessage]);
      setMessageInput("");
    }
  };

  const filteredMessages = messages.filter(msg => msg.channel === activeChannel);

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Breadcrumb Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center gap-3">
          <Button
            onClick={() => router.push('/')}
            className="text-gray-400 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </Button>
          <div className="flex items-center gap-2 text-caption text-gray-500">
            <button 
              onClick={() => router.push('/')}
              className="hover:text-gray-700"
            >
              Trading Hub
            </button>
            <span>/</span>
            <button 
              onClick={() => router.push('/?tab=communities')}
              className="hover:text-gray-700"
            >
              Communities
            </button>
            <span>/</span>
            <span className="text-gray-900 font-medium">{community.name}</span>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - Channels */}
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          {/* Community Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center gap-3 mb-3">
              <img 
                src={community.image} 
                alt={community.name}
                className="w-12 h-12 rounded-lg object-cover"
              />
              <div className="flex-1">
                <h2 className="text-h2 font-bold text-gray-900">{community.name}</h2>
                <div className="flex items-center gap-1 text-body text-gray-500">
                  <UsersIcon className="w-3 h-3" />
                  {community.memberCount.toLocaleString()} members
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button size="1" className="flex-1 bg-gray-50 text-gray-700 hover:bg-gray-100 rounded-lg text-xs font-medium transition-colors">
                <BellIcon className="w-3 h-3 mr-1" />
                Notifications
              </Button>
              <Button size="1" className="bg-gray-50 text-gray-700 hover:bg-gray-100 rounded-lg p-2 transition-colors">
                <SettingsIcon className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* Channels List */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-body font-semibold text-gray-700 uppercase tracking-wide">Channels</h3>
                <Button
                  size="1"
                  onClick={() => setShowCreateChannel(true)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                  </svg>
                </Button>
              </div>
              
              <div className="space-y-1">
                {channels.map((channel) => (
                  <button
                    key={channel.id}
                    onClick={() => setActiveChannel(channel.id)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 ${
                      activeChannel === channel.id
                        ? channel.isAI
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                          : 'bg-gray-50 text-gray-900 border-r-2 border-gray-400'
                        : channel.isAI
                          ? 'text-blue-600 hover:bg-blue-50'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {channel.isAI ? (
                        <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      ) : (
                        <HashIcon className="w-3 h-3 text-gray-400" />
                      )}
                      <span className="text-body font-medium">{channel.name}</span>
                    </div>
                    <p className="text-body text-gray-500 ml-5 mt-0.5 truncate">
                      {channel.description}
                    </p>
                  </button>
                ))}
              </div>

              {/* Create Channel Form */}
              {showCreateChannel && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200" style={{borderWidth: '1px'}}>
                  <h4 className="text-body font-medium text-gray-900 mb-2">Create Channel</h4>
                  <input
                    type="text"
                    placeholder="Channel name"
                    value={newChannelName}
                    onChange={(e) => setNewChannelName(e.target.value)}
                    className="w-full bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm mb-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 transition-colors duration-200"
                    style={{borderWidth: '1px'}}
                  />
                  <input
                    type="text"
                    placeholder="Description (optional)"
                    value={newChannelDescription}
                    onChange={(e) => setNewChannelDescription(e.target.value)}
                    className="w-full bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm mb-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 transition-colors duration-200"
                    style={{borderWidth: '1px'}}
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={handleCreateChannel}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 rounded-lg font-medium transition-colors"
                      style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
                    >
                      Create
                    </Button>
                    <Button
                      onClick={() => setShowCreateChannel(false)}
                      className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs py-2 rounded-lg font-medium transition-colors"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content - Chat */}
        <div className="flex-1 flex flex-col">
          {/* Channel Header */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center gap-2">
              {activeChannel === "ai-assistant" ? (
                <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              ) : (
                <HashIcon className="w-4 h-4 text-gray-400" />
              )}
              <h1 className="text-h1 font-bold text-gray-900">
                {channels.find(c => c.id === activeChannel)?.name}
              </h1>
              <Badge className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-md text-body font-medium">
                {channels.find(c => c.id === activeChannel)?.messageCount} messages
              </Badge>
            </div>
            <p className="text-body text-gray-600 mt-1">
              {channels.find(c => c.id === activeChannel)?.description}
            </p>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {activeChannel === "ai-assistant" ? (
              /* AI Assistant Interface */
              <div className="space-y-4">
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-h3 font-semibold text-gray-900 mb-2">AI Trading Assistant</h3>
                  <p className="text-body text-gray-600 mb-6">
                    Ask me about {community.owner.name}'s recent updates, trading strategies, or community insights!
                  </p>

                  {/* Quick Questions */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3 max-w-2xl mx-auto">
                    {[
                      "What's the latest update?",
                      "Recent trading signals?",
                      "Community performance?"
                    ].map((question, index) => (
                      <button
                        key={index}
                        onClick={() => setMessageInput(question)}
                        className="p-3 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg text-caption font-medium transition-colors"
                      >
                        {question}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              /* Regular Messages */
              filteredMessages.map((message) => (
                <div key={message.id} className="flex gap-3 hover:bg-gray-50 -mx-2 px-2 py-2 rounded-lg transition-colors">
                  <Avatar
                    src={message.user.avatar}
                    fallback={message.user.name[0]}
                    size="2"
                    className="ring-2 ring-gray-100 mt-0.5"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-body font-semibold text-gray-900">
                        {message.user.name}
                      </span>
                      <span className="text-caption text-gray-500">
                        {message.timestamp}
                      </span>
                    </div>
                    <p className="text-body text-gray-700 leading-relaxed">
                      {message.content}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Message Input */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="bg-gray-50 rounded-lg p-3 border border-gray-200" style={{borderWidth: '1px'}}>
              <div className="flex gap-3">
                <Avatar
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face"
                  fallback="You"
                  size="1"
                  className="ring-2 ring-gray-100 mt-1"
                />
                <div className="flex-1 flex gap-2">
                  <textarea
                    placeholder={`Message #${channels.find(c => c.id === activeChannel)?.name}...`}
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && (e.preventDefault(), handleSendMessage())}
                    rows={2}
                    className="flex-1 bg-white border border-gray-200 rounded-lg px-3 py-2 text-body text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 resize-none transition-colors duration-200"
                    style={{borderWidth: '1px'}}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!messageInput.trim()}
                    className={`px-3 py-2 rounded-lg font-medium transition-colors self-end ${
                      messageInput.trim()
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                    style={messageInput.trim() ? {boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'} : {}}
                  >
                    Send
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
