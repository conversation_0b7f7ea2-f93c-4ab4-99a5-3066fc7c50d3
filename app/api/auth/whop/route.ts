import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { whopSdk } from '../../../../lib/whop-sdk';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 Whop auth API called');

    // Get headers from the request
    const headersList = await headers();
    const userToken = headersList.get('x-whop-user-token');

    console.log('📋 Headers received:', {
      hasUserToken: !!userToken,
      userAgent: headersList.get('user-agent'),
      referer: headersList.get('referer')
    });

    if (!userToken) {
      console.log('❌ No x-whop-user-token header found');
      return NextResponse.json({ error: 'No user token provided' }, { status: 401 });
    }

    // Verify user token and get user ID
    const { userId } = await whopSdk.verifyUserToken(headersList);

    if (!userId) {
      console.log('❌ Failed to verify user token');
      return NextResponse.json({ error: 'Invalid user token' }, { status: 401 });
    }

    console.log('✅ Verified user ID:', userId);

    // Get user data by ID
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      console.log('❌ User not found for ID:', userId);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    console.log('✅ Retrieved user data:', {
      id: user.id,
      username: user.username,
      name: user.name,
      hasProfilePicture: !!user.profilePicture?.sourceUrl
    });

    // Format user data for our application
    const formattedUser = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: null, // getUser doesn't return email, only getCurrentUser does
      profilePictureUrl: user.profilePicture?.sourceUrl || null,
      bio: user.bio || null,
      city: user.city || null,
      country: user.country || null,
      phoneVerified: user.phoneVerified || false,
      bannerUrl: user.banner?.sourceUrl || null,
      createdAt: new Date(user.createdAt * 1000).toISOString(),
    };

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error('❌ Error in Whop auth API:', error);
    return NextResponse.json({
      error: 'Failed to authenticate with Whop',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ error: 'No user ID provided' }, { status: 400 });
    }

    console.log('📋 Getting user by ID:', userId);

    // Get user data by ID
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Format user data for our application
    const formattedUser = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: null,
      profilePictureUrl: user.profilePicture?.sourceUrl || null,
      bio: user.bio || null,
      city: user.city || null,
      country: user.country || null,
      phoneVerified: user.phoneVerified || false,
      bannerUrl: user.banner?.sourceUrl || null,
      createdAt: new Date(user.createdAt * 1000).toISOString(),
    };

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error('Error fetching Whop user:', error);
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}
