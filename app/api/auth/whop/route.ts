import { NextRequest, NextResponse } from 'next/server';
import { WhopAPI } from '@whop/api';

const whop = new WhopAPI({
  apiKey: process.env.WHOP_API_KEY!,
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 400 });
    }

    // Verify the token and get user data from Whop
    const user = await whop.users.me({
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Return user data
    return NextResponse.json({
      id: user.id,
      email: user.email,
      username: user.username,
      profilePictureUrl: user.profilePictureUrl,
      discordId: user.discordId,
      createdAt: user.createdAt,
    });
  } catch (error) {
    console.error('Error verifying Whop token:', error);
    return NextResponse.json({ error: 'Failed to verify token' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;
    
    if (!userId) {
      return NextResponse.json({ error: 'No user ID provided' }, { status: 400 });
    }

    // Get user data from Whop using the API key
    const user = await whop.users.retrieve(userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Return user data
    return NextResponse.json({
      id: user.id,
      email: user.email,
      username: user.username,
      profilePictureUrl: user.profilePictureUrl,
      discordId: user.discordId,
      createdAt: user.createdAt,
    });
  } catch (error) {
    console.error('Error fetching Whop user:', error);
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}
