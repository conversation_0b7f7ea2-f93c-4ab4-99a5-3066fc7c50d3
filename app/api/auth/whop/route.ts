import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { whopSdk, formatWhopUserForApp } from '../../../../lib/whop-sdk';

export async function GET(request: NextRequest) {
  try {
    // Get headers from the request
    const headersList = await headers();

    // Verify user token and get user ID
    const { userId } = await whopSdk.verifyUserToken(headersList);

    if (!userId) {
      return NextResponse.json({ error: 'No valid user token' }, { status: 401 });
    }

    // Get current user data
    const currentUserResponse = await whopSdk.users.getCurrentUser();

    if (!currentUserResponse?.user) {
      return NextResponse.json({ error: 'Failed to get user data' }, { status: 404 });
    }

    // Format user data for our application
    const formattedUser = formatWhopUserForApp(currentUserResponse.user);

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error('Error verifying Whop token:', error);
    return NextResponse.json({ error: 'Failed to verify token' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ error: 'No user ID provided' }, { status: 400 });
    }

    // Get user data by ID
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Format user data for our application
    const formattedUser = formatWhopUserForApp(user);

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error('Error fetching Whop user:', error);
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}
